import OnlineOrderItem from '../models/subOnline-OrderItem.js'
import { User } from '../models/User.js'
import Product from '../models/product.js';
import IngredientModel from '../models/ingredients.js';
import sendMail from '../middlewares/send-email.js';
import mongoose from 'mongoose';
import moment from 'moment';
import device from '../models/device.js';
import { generateOrderReadyEmailTemplate } from '../templates/orderReadyEmailTemplate.js';


export const GetOnlineOrderItem = async (req, res) => {
  console.log("Query Parameters:", req.query);
  let filter;
  if (req.query.userId) {
    filter = { userId: req.query.userId.split(',') }
  }
  const onlineOrderData = await OnlineOrderItem.find(filter).populate('userId').populate('customerId');
  res.send(onlineOrderData)
}
export const getOnlineKitchenOrders = async (req, res) => {
  const { userId } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  const matchCondition = {
    userId: new mongoose.Types.ObjectId(userId),
  };

  const endTime = new Date();
  endTime.setHours(23, 59, 59, 999);

  const startTime = new Date(endTime.getTime() - 48 * 60 * 60 * 1000);

  matchCondition.createdAt = {
    $gte: startTime,
    $lte: endTime,
  };

  try {
    console.log("Match Condition:", matchCondition);

    const orders = await OnlineOrderItem.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: "tables",
          localField: "table",
          foreignField: "_id",
          as: "table",
        },
      },
       {
        $lookup: {
          from: "patronpalcustomers",
          localField: "customerId",
          foreignField: "_id",
          as: "customerId",
        },
      },
      { $unwind: { path: "$customerId", preserveNullAndEmptyArrays: true } },
      {
        $project: {
          product: 1,
          orderStatus: 1,
          Status: 1,
          createdAt: 1,
          customerId: 1,
          orderNo: 1
        },
      },
    ]);

    res.send(orders);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

// export const getOnlineOrderItemsLastWeek = async (req, res) => {
//   const { userId } = req.query;

//   if (!userId) {
//     return res.status(400).send("User ID is required");
//   }

//   try {
//     const userObjectId = new mongoose.Types.ObjectId(userId);

//     const startDate = moment().subtract(7, 'days').startOf('day').toDate();

//     const endDate = moment().endOf('day').toDate();

//     const data = await OnlineOrderItem.find({
//       userId: userObjectId,
//       createdAt: { $gte: startDate, $lte: endDate },
//     })
//       .populate('userId', 'name email')
//       .populate('customerId', 'name email')
//       .lean()
//       .exec();

//     res.json(data);
//   } catch (error) {
//     console.error(error);
//     res.status(500).send("Internal Server Error");
//   }
// };


export const getOnlineOrderItemsLastWeek = async (req, res) => {
  const { userId } = req.query;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // Start date: 7 days ago, at 00:00:00
    const startDate = moment().subtract(7, 'days').startOf('day').toDate();

    // End date: today, at 23:59:59
    const endDate = moment().endOf('day').toDate();

    console.log("Start Date:", startDate);
    console.log("End Date:", endDate);

    const data = await OnlineOrderItem.find({
      userId: userObjectId,
      createdAt: { $gte: startDate, $lte: endDate },
    })
      .populate('userId', 'name email')
      .populate('customerId')
      .lean()
      .exec();

    res.json(data);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const GetOnlineOrderItemById = async (req, res) => {
  console.log("Query Parameters *********:", req.params);
  
  const onlineOrderItem = await OnlineOrderItem.find(req.params).populate('userId').populate('customerId');
  res.send(onlineOrderItem)
}


export const PostOnlineOrderItem = async (req, res) => {
  const { Status, orderType, deliveryOptions, pickUpObj, orderStatus, tax, parentOrderId, product, loyalityOffer, couponOffer, customerId, chargeId, paymentIntentId, userId, PaymentStatus, deliveryfee, Amount, OrderNumber } = req.body;

  // Temporary order number - will be updated by parent order creation
  const orderNo = 'TEMP_ORDER_NO';
  const data = new OnlineOrderItem({ orderNo, Status, orderType, deliveryOptions, pickUpObj, orderStatus, tax, parentOrderId, product, loyalityOffer, couponOffer, customerId, chargeId, paymentIntentId, userId, PaymentStatus, deliveryfee, Amount, OrderNumber })
  let prod = []
  prod = product
  await data.save().then(async (result) => {
    // const customerPoints = priceExclTax / 5;
    // const customerById = await customer.findById(customerId)
    if (Array.isArray(prod) && prod.length > 0) {
      prod.map(async (item) => {
        console.log('item: ', item);
        const products = await Product.findById({ _id: item._id })
          .populate('userId')
          .populate({
            path: "ingredient",
            populate: { path: "ingredient.ingredientId", model: "ingredientsModel" }
          });
        console.log('products: ', products);
        // await Product.findByIdAndUpdate({ _id: products._id }, { $set: { "totalQuantity": products.totalQuantity - item.quantity } })
        const totalQuantity = parseFloat(products.totalQuantity);
        const quantity = parseFloat(item.quantity);

        if (!isNaN(totalQuantity) && !isNaN(quantity)) {
          const updatedTotalQuantity = totalQuantity - quantity;
          await Product.findByIdAndUpdate(item._id, { $set: { "totalQuantity": updatedTotalQuantity } });
        }
        let filteredProductsName = []
        let userEmail = products.userId.email
        if (products.totalQuantity <= 5) {
          filteredProductsName.push(products.name)
        }
        if (products.ingredient && products.ingredient.length > 0) {
          const ingredients = [];
          for (const ingredientItem of products.ingredient) {
            if (ingredientItem.ingredientId) {
              const ingredientId = ingredientItem.ingredientId;
              const user = await User.findOne({ _id: userId });
              const ingredient = await IngredientModel.findOne({ _id: ingredientId });
              if (ingredient?.stockHistory && ingredient?.stockHistory?.length > 0) {
                const sortedStockHistory = ingredient.stockHistory.sort((a, b) => {
                  const dateA = new Date(a.expiry);
                  const dateB = new Date(b.expiry);
                  return dateA - dateB;
                });
                let remainingQuantity = ingredientItem.quantity * item.quantity;
                let stocksToRemove = [];
                for (const stockItem of sortedStockHistory) {
                  if (remainingQuantity <= 0) {
                    break;
                  }
                  if (stockItem.stock >= remainingQuantity) {
                    stockItem.stock -= remainingQuantity;
                    remainingQuantity = 0;
                  } else {
                    remainingQuantity -= stockItem.stock;
                    stockItem.stock = 0;
                  }
                  if (stockItem.stock === 0) {
                    stocksToRemove.push(stockItem);
                  }
                }
                for (const stockToRemove of stocksToRemove) {
                  const indexToRemove = ingredient.stockHistory.indexOf(stockToRemove);
                  if (indexToRemove !== -1) {
                    ingredient.stockHistory.splice(indexToRemove, 1);
                  }
                }
                let totalStock = 0;

                for (const stockItem of ingredient.stockHistory) {
                  totalStock += stockItem.stock;
                }
                if (totalStock < ingredient.ThresholdLevel) {
                  const email = user.email
                  await sendMail(email, "Stock quantity are decrease ", `<html>
  
        <head>
        <style>
        
        .logo{
        width: 30%;
        }
        a{
        color:blue;
        cursor:pointer;
        text-decoration:none;}
        .maindivdata{
        padding:2rem 4rem;
        border: 1px solid lightgray;}
        
        .client{
         color: white;
         font-weight: 700;
         display: flex;
         font-size: 25px;
         width: 100%;
         justify-content:center;
         padding-top: 10rem;
         padding-left:20px;
        }
     
     .power{
     font-size:12px;
     color:gray;
     }
     p{
     font-size:16px;
         font-family: 'Poppins', sans-serif;
     
     }
    
     
        .container{
        width:50%;
        margin:auto;
            font-family: 'Poppins', sans-serif;
     
     
        
        }
        .infologo{
          background:transparent;
          border:none;
        }
        .shortimg{
          width:20px;
          height:20px;
          }
    
     h3{
            font-family: 'Poppins', sans-serif;
     
     }
     span{
            font-family: 'Poppins', sans-serif;
     
     }
     h5{
            font-family: 'Poppins', sans-serif;
     
     }
        @media screen and (max-width:900px) {
        .container{
        width:100%;
        margin:0px;
        
        }
        .client{
         color: white;
         font-weight: 700;
         display: grid;
         font-size: 25px;
         width: 100%;
         padding-top: 10rem;
         padding-left:10px;
        }
      .maindivdata{
        padding:2rem 10px;
        }
        .btn{
          font-size:12px
                  }
                  
        
        }
        
        </style>
          <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
     
        </head>
           <body>
           <div class="container">
           <div style="font-family: Arial, Helvetica, sans-serif; ">
           <div style="width: auto; height: 4rem;background-color: rgb(6, 138, 245); ">
           
               
           
           </div>
           <div class="maindivdata">
         <div class="top" style="  display:flex; 
         justify-content:center !important; align-items:center;"> 
     <img class="image" style=" justify-self:center ; margin-left:20%; display:flex; justify-content:center !important; align-items:center; width:60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
         </div>
                    <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${email}</strong></p>
     
               <p style=" margin-bottom: 2rem;">We are excited to have you join us at PatronWorks.</p>
                         
               <p style=" margin-bottom: 2rem;">Please Add Stock on <strong>${ingredient.IngredientName}<strong> </p>
     
           
              
               <p style=" margin-bottom: 2rem;">We look forward to serving you.</p>
               <p style=" margin-bottom: 2rem;"> If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL>. </strong></p>
               <p style=" margin-bottom: 2rem;">Thank you for choosing PatronWorks for your business!</p>
               
      
      <hr>
        
     <div style="display:flex; justify-content:space-between; margin-top:1rem;">
        <div>
        <img style="width:60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
        </div>
        <div style="display:flex; margin-left:45%;">
        <a style="margin-right:10px; href="https://www.linkedin.com/company/patronworks/">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
        </a>
        
        <a href="https://www.facebook.com/patronworks">
          <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
        </a>
      </div>
  
        </div>
              
        
             </div>
        </div>
        </div>
        </body>
        </html>`)
                  console.log(`Quantity is less in amount. Total stock: ${totalStock}, ThresholdLevel: ${ingredient.ThresholdLevel}`);
                }
                await ingredient.save();
              }

              ingredients.push(ingredient);
            }
          }
          console.log("ingredients: ", ingredients);
          return ingredients;
        }



        if (products.totalQuantity <= 5 && userEmail && filteredProductsName) {
          sendMail(userEmail, "Low Stock Alerts", `<h2 style="background-color: #f1f1f1; padding: 20px;width:50%">These Products Are  Low  In  Stock</h2><br><h3 style="background-color: #f1f1f1; width:60%">${filteredProductsName}</h3>`)
        }
      })
    } else {
      console.warn('Invalid or empty "product" array');
    }
    res.json({
      _id: result._id,
      orderNo: result.orderNo,
      OrderNumber: result.OrderNumber,
      Status: result.Status,
      orderType: result.orderType,
      deliveryOptions: result.deliveryOptions,
      pickUpObj: result.pickUpObj,
      orderStatus: result.orderStatus,
      tax: result.tax,
      product: result.product,
      loyalityOffer: result.loyalityOffer,
      couponOffer: result.couponOffer,
      customerId: result.customerId,
      userId: result.userId,
      PaymentStatus: result.PaymentStatus,
      deliveryfee: deliveryfee,
      Amount: result.Amount,
      paymentIntentId: result.paymentIntentId,
      chargeId: result.chargeId
    })
  })
}

export const updateOnlineOrderItem = async (req, res) => {
  try {
    // Get the current order data before updating
    const currentOrder = await OnlineOrderItem.findById(req.params._id).populate('customerId').populate('userId');

    const updatedData = await OnlineOrderItem.findByIdAndUpdate(
      { _id: req.params._id },
      { $set: req.body },
      { new: true }
    ).populate('customerId').populate('userId');

    if (updatedData) {
    
      if (req.body.orderStatus === 'ready' && currentOrder.orderStatus !== 'ready') {
        try {
          await sendOrderReadyEmail(updatedData);
        } catch (emailError) {
          console.error('Error sending order ready email:', emailError);
          // Don't fail the update if email fails
        }
      }

      res.send({ message: "Online Orderitem data updated successfully", updatedData });
    } else {
      res.send({ message: "Online Orderitem data cannot be updated successfully", updatedData });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: "Internal server error" });
  }
}

// Helper function to send order ready email
const sendOrderReadyEmail = async (orderData) => {
  try {
    const customer = orderData.customerId;
    const user = orderData.userId;

    if (!customer || !customer.Email) {
      console.log('No customer email found for order:', orderData.orderNo);
      return;
    }

    // Get device/restaurant information
    const deviceInfo = await device.findOne({ userId: user._id });

    const customerName = `${customer.FirstName || ''} ${customer.LastName || ''}`.trim() || 'Valued Customer';
    const businessName = deviceInfo?.name || user?.businessName || user?.name || 'Restaurant';
    const orderType = orderData.orderType || 'pickup';
    const orderNo = orderData.orderNo || orderData.OrderNumber || 'N/A';

    // Get API base URL for form submission
    let apiBaseUrl;
    if (process.env.NODE_ENV === 'production') {
      apiBaseUrl = 'https://api.betaversion.patronworks.net'; // Your production domain
    } else {
      apiBaseUrl = 'http://localhost:4444'; // Your local backend
    }

    // Generate email template with embedded form
    const emailHtml = generateOrderReadyEmailTemplate(
      customerName,
      orderType,
      orderNo,
      businessName,
      deviceInfo,
      apiBaseUrl,
      orderData._id,
      customer._id,
      user._id
    );

    // Send email
    const subject = `Your Order #${orderNo} is Ready ${orderType === 'delivery' ? 'for Delivery' : 'for Pickup'}!`;
    await sendMail(customer.Email, subject, emailHtml);

    console.log(`Order ready email sent to ${customer.Email} for order ${orderNo}`);
  } catch (error) {
    console.error('Error in sendOrderReadyEmail:', error);
    throw error;
  }
};
export const deleteOnlineOrderItem = async (req, res) => {
  try {
    const deletedData = await OnlineOrderItem.findByIdAndDelete(
      req.params.id 
    );

    if (deletedData) {
      res.send({ message: "Online Orderitem data deleted successfully", deletedData });
    } else {
      res.send({ message: "Online Orderitem data cannot be deleted successfully", deletedData });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: "Internal server error" });
  }
}