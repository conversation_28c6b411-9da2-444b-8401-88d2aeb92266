'use strict';

/**
 * Split name and email address from string
 */
module.exports = function splitNameEmail(str) {

  //If no email bracket present, return as is
  if (str.indexOf('<') === -1) {
    return ['', str];
  }

  //Split into name and email
  let [name, email] = str.split('<');

  //Trim and fix up
  name = name.trim();
  email = email.replace('>', '').trim();

  //Return as array
  return [name, email];
};
