'use strict';

/**
 * Dependencies
 */
const Personalization = require('../personalization');
const EmailAddress = require('../email-address');

/**
 * Tests
 */
describe('Personalization', function() {

  //Create new personalization before each test
  let p;
  beforeEach(function() {
    p = new Personalization();
  });

  // camel case headers test
  describe('#527', function() {
    it('shouldn\'t convert the headers to camel/snake case', function() {
      const p = new Personalization({
        to: '<EMAIL>',
        headers: {
          'List-Unsubscribe': '<mailto:<EMAIL>>',
        },
      });

      expect(p.headers['List-Unsubscribe']).to.equal('<mailto:<EMAIL>>');

      expect(p.toJSON().headers['List-Unsubscribe']).to
        .equal('<mailto:<EMAIL>>');
    });
  });
});
