import './config/config.js';

import bodyParser from 'body-parser';
import cors from 'cors';
import dotenv from 'dotenv';
import express from 'express';
import helmet from 'helmet';
import morgan from 'morgan';
import onvif from 'node-onvif';
import { Server } from 'socket.io';
import twilio from "twilio";

import axios from 'axios'

import blog from './api-routes/blog-route.js';
import camera from './api-routes/camera-route.js';
import category from './api-routes/category-route.js';
import chatRoute from './api-routes/chat_route.js';
import check from './api-routes/check-route.js';
import Checkout from './api-routes/checkout-route.js';
import contactus from './api-routes/contactUs-route.js';
import coupens from './api-routes/coupens-route.js';
import customer from './api-routes/customer-route.js';
import customization from './api-routes/customization-route.js';
import billdenomination from './api-routes/denomination-route.js';
import device from './api-routes/device-route.js';
import display from './api-routes/display-route.js';
import emailMarketing from './api-routes/emailMarketing-route.js';
import employee from './api-routes/employee-route.js';
import employeTimeStamp from './api-routes/employeetime-route.js';
import Ingredientcategory from './api-routes/ingredient-category-route.js';
import Ingredients from './api-routes/ingredients-route.js';
import Loyaltyoffers from './api-routes/loaylty-offers-route.js';
import logo from './api-routes/logo-route.js';
import menu from './api-routes/menu-route.js';
import mu from './api-routes/mu-route.js';
import order from './api-routes/order-route.js';
import orderitem from './api-routes/orderitem-route.js';
import parentcategory from './api-routes/parentcategory-route.js';
import paymentlist from './api-routes/paymentlist-route.js';
import modifier from './api-routes/prdouct-modifier-route.js';
import Print from './api-routes/printer-route.js';
import product from './api-routes/product-route.js';
import recieptExampt from './api-routes/reciept-exampt.js';
import reciept from './api-routes/reciept-route.js';
import tableReservation from './api-routes/reservation&waitingList-route.js';
import review from './api-routes/review-route.js';
import role from './api-routes/role-route.js';
import Site from './api-routes/sitemanagement-route.js';
import smsMarketing from './api-routes/smsmarketing-route.js';
import StockWastage from './api-routes/stock-wastage-route.js';
import Supplier from './api-routes/supplier-route.js';
import tables from './api-routes/table-route.js';
import tax from './api-routes/tax-route.js';
import Auth from './api-routes/user-route.js';
import userRegisterWithEmailVerification from './api/emailVerification.js';
import passwordreset from './api/reset-password.js';
import cancelsub from './api-routes/cancel-subs-route.js'
import twilioApi from './api-routes/twilio-route.js';
import party from './api-routes/playlandparty-route.js'
import playlandOrder from './api-routes/playlandOrder-route.js'
import patronpalcustomer from './api-routes/patronpalCustomer-route.js'
import parentOnlineOrder from './api-routes/parentonlineorder-route.js'
import subOnlineOrder from './api-routes/subonline0rder-route.js'
import referral from './api-routes/referral-routes.js'
import holdOrder from './api-routes/holdOrders-route.js'
import reports from './api-routes/reports-route.js'
import sendMail from './middlewares/send-email.js';
import automation  from "./api-routes/automation-route.js";
import yelp from "./api-routes/yelp-route.js";
const app = express();
dotenv.config();
//middelwares
app.use(cors({
    origin: true,
    // origin: ['http://localhost:4200', 'http://betaversion.patronworks.net/', 'https://dev.patronworks.net/'],
    credentials: true,
    defaultErrorHandler: false,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Debug middleware for form submissions
app.use('/api/v1/device/email-review', (req, res, next) => {
    console.log('=== EMAIL REVIEW DEBUG ===');
    console.log('Content-Type:', req.headers['content-type']);
    console.log('Method:', req.method);
    console.log('Raw Body:', req.body);
    console.log('Body Keys:', Object.keys(req.body || {}));
    console.log('========================');
    next();
});

app.use(helmet({ crossOriginResourcePolicy: false, }));
// app.use(
//   helmet({
//     referrerPolicy: { policy: 'origin' },
//     crossOriginResourcePolicy: false,
//   })
// );
app.use(morgan("dev"));
//uset Email Verification Endpoints
app.use('/api/v1/activate-account', userRegisterWithEmailVerification)
//user forgot and reset-password Endpoints
app.use('/api/v1/reset-password', passwordreset)
//All APi's Endponits
app.use('/api/v1', Auth, category, check, device, display, employee, menu, mu, order, orderitem, paymentlist, product, role, tax, tables, parentcategory, customer, Checkout, modifier, tableReservation, emailMarketing, smsMarketing, Loyaltyoffers, customization, logo, blog, contactus, employeTimeStamp, reciept, coupens, chatRoute, billdenomination,recieptExampt,camera,Supplier,Ingredients,Ingredientcategory,StockWastage,Print,Site,review,cancelsub,twilioApi, party, playlandOrder, patronpalcustomer,parentOnlineOrder,subOnlineOrder, referral, holdOrder, reports , automation , yelp)
let NODESERVER = null;


app.post('/api/v1/send-complaint', async (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;

    if (!name || !email || !subject || !message) {
      return res.status(400).json({ error: 'Missing required fields.' });
    }

    const emailContent = `
      <p>You have received a new complaint:</p>
      <ul>
        <li><b>Name:</b> ${name}</li>
        <li><b>Email:</b> ${email}</li>
        <li><b>Phone:</b> ${phone || 'N/A'}</li>
        <li><b>Subject:</b> ${subject}</li>
      </ul>
      <p><b>Message:</b></p>
      <p>${message}</p>
    `;

    // Send email to admin group
    const recipients = "<EMAIL>, <EMAIL>";
    // const recipients = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
    await sendMail(recipients, `New Complaint: ${subject}`, emailContent);

    return res.status(200).json({ message: 'Your complaint has been sent successfully.' });
  } catch (error) {
    console.error('Complaint Error:', error);
    return res.status(500).json({ error: 'Something went wrong. Please try again later.' });
  }
});



app.post('/voice', (req, res) => {
    const twiml = new twilio.twiml.VoiceResponse();

    // Use <Gather> to capture voice input from the customer
    const gather = twiml.gather({
        input: 'speech',
        action: '/process-speech',
        speechTimeout: 'auto',
        speechModel: 'default'
    });
    gather.say('Welcome to our service. How can I assist you today?');

    res.type('text/xml');
    res.send(twiml.toString());
});

app.post('/process-speech', async (req, res) => {
    try {
        const speechResult = req.body.SpeechResult;
        console.log(`Customer said: ${speechResult}`);

        if (!speechResult) {
            console.log('No speech input detected.');
            const twiml = new twilio.twiml.VoiceResponse();
            twiml.say('We did not receive any input. Please try again.');
            twiml.redirect('/voice');
            res.type('text/xml');
            res.send(twiml.toString());
        } else {
            const aiResponse = await getAIResponse(speechResult);
            console.log(`AI Response: ${aiResponse}`);

            const twiml = new twilio.twiml.VoiceResponse();
            twiml.say(aiResponse);

            // Record the call
            twiml.record({ transcribe: true, maxLength: 120, playBeep: true });

            res.type('text/xml');
            res.send(twiml.toString());
        }
    } catch (error) {
        console.error('Error processing speech:', error);
        const twiml = new twilio.twiml.VoiceResponse();
        twiml.say('We are sorry, an application error has occurred. Goodbye.');
        res.type('text/xml');
        res.send(twiml.toString());
    }
});


async function getAIResponse(speech) {
    try {
        console.log(`Sending speech to AI service: ${speech}`);
        const response = await axios.post('https://api.patronworks.net/voice', {
            queryInput: {
                text: {
                    text: speech,
                    languageCode: 'en-US'
                }
            }
        });

        console.log('Received response from AI service:', response.data);

        if (response.data && response.data.queryResult) {
            return response.data.queryResult.fulfillmentText || 'Sorry, I did not understand that.';
        } else {
            console.log('Invalid response from AI service');
            throw new Error('Invalid response from AI service');
        }
    } catch (error) {
        console.error('Error getting AI response:', error.message);
        console.error('Error details:', error.response ? error.response.data : 'No response data');
        return 'I am sorry, but I am unable to process your request at the moment.';
    }
}


//Port
if (process.env.NODE_ENV === 'production') {
    app.use('*', (req, res) => {
        return res.status(404).json({
            success: false,
            message: 'API endpoint doesnt exist please put Api prod routes ..'
        })
    });
    const port = process.env.PORT || 3333;
    NODESERVER = app.listen(port, () => {
        console.log(`Server is running on port: ${port}`);
    });
} else if (process.env.NODE_ENV === 'development') {
    app.get('/getCameraStreamUrl', async (req, res) => {
        const { cameraAddress, cameraPort, cameraUsername, cameraPassword } = req.query;
      
        try {
          // Discover the ONVIF camera
          console.log('Start the discovery process.');
          const device_info_list = await onvif.startProbe();
      
          if (device_info_list.length === 0) {
            return res.status(404).json({ error: 'No devices were found.' });
          }
      
          // Assuming there's at least one camera discovered
          const cam = new onvif.OnvifDevice({
            xaddr: `http://${cameraAddress}:${cameraPort}/onvif/device_service`,
            user: cameraUsername,
            pass: cameraPassword,
          });   
      
          await cam.init();
      
          // Get the UDP stream URL
          const streamUrl = cam.getUdpStreamUrl();
          console.log('Stream URL:', streamUrl);
      
          // Respond with the stream URL
          res.json({ streamUrl });
        } catch (error) {
          console.error('Error discovering camera or fetching stream URL:', error);
          res.status(500).json({ error: 'Error discovering camera or fetching stream URL' });
        }
      });
    app.use('*', (req, res) => {
        return res.status(404).json({
            success: false,
            message: 'API endpoint doesnt exist please put Api dev routes ..'
        })
    });
    const port = process.env.DEV_PORT || 4444;
    NODESERVER = app.listen(port, () => {
        console.log(`Server is running on port: ${port}`);
    });
}

console.log('NODESERVER: ', NODESERVER);


const io = new Server(NODESERVER, {
    pingTimeout: 60000,
    cors: {
        origin: [process.env.LOCAL_LINK1, process.env.LOCAL_LINK2, process.env.PROD_LINK1, process.env.PROD_LINK2],
        // credentials: true,
    },
});

io.on("connection", (socket) => {
    console.log("A user connected:", socket.id);

    socket.on("setup", (userData) => {
        socket.join(userData.userId);
        socket.emit("me", userData.userId);
        socket.emit("connected");
    });

    socket.on("new_message", (newMessageReceived) => {
        var chat = newMessageReceived.chat;
        if (!chat) return console.log("chat.users not defined");

        if (chat.Admin == newMessageReceived.senderId) {
            io.to(chat.user).emit("messagerecieved", newMessageReceived);
        }
        if (chat.user == newMessageReceived.senderId) {
            io.to(chat.Admin).emit("messagerecieved", newMessageReceived);
        }
    });

    socket.on("disconnect", () => {
        console.log("A user disconnected:", socket.id);
    });
});

