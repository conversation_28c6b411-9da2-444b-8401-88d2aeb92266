<!DOCTYPE html>
<html>
<head>
    <title>Debug Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        input[type="text"], textarea { width: 300px; }
        input[type="radio"] { margin-right: 8px; }
        .radio-group { margin: 10px 0; }
        .submit-btn { background: #068af5; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        .debug-info { background: #f0f0f0; padding: 15px; margin: 15px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔍 Debug Form Test</h2>
        <p>This form will help us debug the form submission issue.</p>
        
        <div class="debug-info">
            <strong>Test Endpoints:</strong><br>
            • Test: http://localhost:4444/api/v1/device/test-review<br>
            • Actual: http://localhost:4444/api/v1/device/email-review
        </div>

        <!-- Test Form 1: Simple test endpoint -->
        <h3>Test 1: Simple Debug Endpoint</h3>
        <form action="http://localhost:4444/api/v1/device/test-review" method="POST">
            <div class="form-group">
                <label>Device ID:</label>
                <input type="text" name="deviceId" value="507f1f77bcf86cd799439011" required>
            </div>
            <div class="form-group">
                <label>Customer ID:</label>
                <input type="text" name="customerId" value="507f1f77bcf86cd799439012" required>
            </div>
            <div class="form-group">
                <label>Order ID:</label>
                <input type="text" name="orderId" value="test_order_123">
            </div>
            <div class="form-group">
                <label>Rating (Select One):</label>
                <div class="radio-group">
                    <label><input type="radio" name="rating" value="5" required> 5 - Excellent</label>
                    <label><input type="radio" name="rating" value="4"> 4 - Very Good</label>
                    <label><input type="radio" name="rating" value="3"> 3 - Good</label>
                    <label><input type="radio" name="rating" value="2"> 2 - Fair</label>
                    <label><input type="radio" name="rating" value="1"> 1 - Poor</label>
                </div>
            </div>
            <div class="form-group">
                <label>Comment (Optional):</label>
                <textarea name="testimonial" placeholder="Your feedback..."></textarea>
            </div>
            <button type="submit" class="submit-btn">Test Debug Endpoint</button>
        </form>

        <hr style="margin: 30px 0;">

        <!-- Test Form 2: Actual review endpoint -->
        <h3>Test 2: Actual Review Endpoint</h3>
        <form action="http://localhost:4444/api/v1/device/email-review" method="POST">
            <div class="form-group">
                <label>Device ID:</label>
                <input type="text" name="deviceId" value="507f1f77bcf86cd799439011" required>
            </div>
            <div class="form-group">
                <label>Customer ID:</label>
                <input type="text" name="customerId" value="507f1f77bcf86cd799439012" required>
            </div>
            <div class="form-group">
                <label>Order ID:</label>
                <input type="text" name="orderId" value="test_order_123">
            </div>
            <div class="form-group">
                <label>Rating (Select One):</label>
                <div class="radio-group">
                    <label><input type="radio" name="rating" value="5" required> 5 - Excellent</label>
                    <label><input type="radio" name="rating" value="4"> 4 - Very Good</label>
                    <label><input type="radio" name="rating" value="3"> 3 - Good</label>
                    <label><input type="radio" name="rating" value="2"> 2 - Fair</label>
                    <label><input type="radio" name="rating" value="1"> 1 - Poor</label>
                </div>
            </div>
            <div class="form-group">
                <label>Comment (Optional):</label>
                <textarea name="testimonial" placeholder="Your feedback..."></textarea>
            </div>
            <button type="submit" class="submit-btn">Submit Review</button>
        </form>

        <div class="debug-info">
            <h3>Testing Steps:</h3>
            <ol>
                <li><strong>Start your server:</strong> <code>npm run dev</code></li>
                <li><strong>Test Form 1:</strong> Fill out and submit to see debug info</li>
                <li><strong>Check server logs</strong> for the debug middleware output</li>
                <li><strong>Test Form 2:</strong> Try the actual review endpoint</li>
                <li><strong>Compare results</strong> to identify the issue</li>
            </ol>
            
            <h3>Expected Server Logs:</h3>
            <pre style="background: #f8f8f8; padding: 10px; font-size: 12px;">
=== EMAIL REVIEW DEBUG ===
Content-Type: application/x-www-form-urlencoded
Method: POST
Raw Body: { deviceId: '507f...', customerId: '507f...', rating: '5', ... }
Body Keys: [ 'deviceId', 'customerId', 'orderId', 'rating', 'testimonial' ]
========================
            </pre>
        </div>
    </div>
</body>
</html>
