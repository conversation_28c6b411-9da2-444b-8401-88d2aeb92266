// Email template for order ready notifications with embedded review form
export const generateOrderReadyEmailTemplate = (
  customerName,
  orderType,
  orderNo,
  businessName,
  deviceInfo,
  apiBaseUrl,
  orderId,
  customerId
) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';

  return `
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: 'Poppins', sans-serif;
          background-color: #f8f8f8;
          margin: 0;
          padding: 0;
        }
        .container {
          width: 90%;
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header-bar {
          height: 4rem;
          background-color: #068af5;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        .logo {
          display: block;
          margin: 30px auto;
          max-width: 180px;
        }
        .order-details {
          background-color: #e8f4fd;
          padding: 15px;
          border-left: 4px solid #068af5;
          margin: 20px 0;
          border-radius: 5px;
        }
        .order-details h3 {
          margin: 0 0 10px;
          color: #068af5;
        }
        .review-section {
          background-color: #f0f8ff;
          padding: 25px;
          border-radius: 8px;
          border: 2px solid #068af5;
          margin: 30px 0;
        }
        .review-form {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .rating-label {
          font-weight: bold;
          margin-bottom: 10px;
          display: block;
          color: #333;
        }
        .rating-options {
          margin-bottom: 20px;
        }
        .rating-option {
          display: block;
          margin: 10px 0;
          padding: 12px;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          background-color: #fff;
          cursor: pointer;
        }
        .rating-option:hover {
          border-color: #068af5;
          background-color: #f8f9ff;
        }
        .rating-option input[type="radio"] {
          margin-right: 10px;
          transform: scale(1.2);
        }
        .star-display {
          font-size: 18px;
          color: #ffc107;
          margin-right: 8px;
        }
        .rating-text {
          font-size: 16px;
          color: #333;
          font-weight: bold;
        }
        .form-group {
          margin-bottom: 15px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          min-height: 80px;
          resize: vertical;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 12px 30px;
          border: none;
          border-radius: 5px;
          font-weight: bold;
          cursor: pointer;
          font-size: 16px;
          transition: background-color 0.3s;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #888;
          margin-top: 30px;
        }
        .social-icons img {
          width: 20px;
          margin: 0 5px;
        }
        @media screen and (max-width: 600px) {
          .container {
            padding: 20px;
          }
          .logo {
            max-width: 140px;
          }
        }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    </head>
    <body>
      <div class="header-bar"></div>
      <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>${customerName}</strong>,</p>

        <div class="order-details">
          <h3>🎉 Great News! Your Order is Ready</h3>
          <p><strong>Order Number:</strong> ${orderNo}</p>
          <p><strong>Status:</strong> Your order is <strong>${readyMessage}</strong>.</p>
          <p>${actionMessage}</p>
        </div>

        <p>Thank you for choosing <strong>${businessName}</strong>.</p>
        ${isDelivery 
          ? `<p>Our delivery team will be reaching out to you shortly.</p>` 
          : `<p>Please visit our location to collect your order at your convenience.</p>`}

        <div class="review-section">
          <h3>📝 We Value Your Feedback!</h3>
          <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

          <div class="review-form">
            <form action="${apiBaseUrl}/api/v1/device/email-review" method="POST">
              <input type="hidden" name="deviceId" value="${deviceInfo?._id || deviceInfo?.id || ''}">
              <input type="hidden" name="customerId" value="${customerId || ''}">
              <input type="hidden" name="orderId" value="${orderId || ''}">

              <!-- Debug info (remove in production) -->
              <div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px; border-radius: 4px;">
                <strong>Debug Info:</strong><br>
                Device ID: ${deviceInfo?._id || deviceInfo?.id || 'NOT_SET'}<br>
                Customer ID: ${customerId || 'NOT_SET'}<br>
                Order ID: ${orderId || 'NOT_SET'}<br>
                API URL: ${apiBaseUrl || 'NOT_SET'}
              </div>

              <label class="rating-label">⭐ Rate Your Overall Experience (Select One)</label>
              <div class="rating-options">
                <label class="rating-option" for="rating_5">
                  <input type="radio" name="rating" value="5" id="rating_5" required>
                  <span class="star-display">⭐⭐⭐⭐⭐</span>
                  <span class="rating-text">5 - Excellent</span>
                </label>

                <label class="rating-option" for="rating_4">
                  <input type="radio" name="rating" value="4" id="rating_4">
                  <span class="star-display">⭐⭐⭐⭐</span>
                  <span class="rating-text">4 - Very Good</span>
                </label>

                <label class="rating-option" for="rating_3">
                  <input type="radio" name="rating" value="3" id="rating_3">
                  <span class="star-display">⭐⭐⭐</span>
                  <span class="rating-text">3 - Good</span>
                </label>

                <label class="rating-option" for="rating_2">
                  <input type="radio" name="rating" value="2" id="rating_2">
                  <span class="star-display">⭐⭐</span>
                  <span class="rating-text">2 - Fair</span>
                </label>

                <label class="rating-option" for="rating_1">
                  <input type="radio" name="rating" value="1" id="rating_1">
                  <span class="star-display">⭐</span>
                  <span class="rating-text">1 - Poor</span>
                </label>
              </div>

              <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience at ${businessName}..."></textarea>
              </div>

              <input type="submit" class="submit-button" value="Submit Review" />
            </form>
            <p style="font-size: 13px; color: #555;"><em>Thank you for your feedback!</em></p>
          </div>
        </div>

        <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
          <p>Follow us:</p>
          <div class="social-icons">
            <a href="https://www.linkedin.com/company/patronworks/"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn" /></a>
            <a href="https://www.facebook.com/patronworks"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook" /></a>
          </div>
          <p>&copy; ${new Date().getFullYear()} ${businessName}. All rights reserved.</p>
        </div>
      </div>


    </body>
  </html>
  `;
};
