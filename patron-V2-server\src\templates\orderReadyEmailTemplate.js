// Email template for order ready notifications with pure CSS star rating
export const generateOrderReadyEmailTemplate = (
  customerName,
  orderType,
  orderNo,
  businessName,
  deviceInfo,
  apiBaseUrl,
  orderId,
  customerId
) => {
  const isDelivery = orderType === 'delivery';
  const readyMessage = isDelivery ? 'ready for delivery' : 'ready for pickup';
  const actionMessage = isDelivery ? 'Your order will be delivered soon!' : 'Please come to pick up your order.';

  return `
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: 'Poppins', sans-serif;
          background-color: #f8f8f8;
          margin: 0;
          padding: 0;
        }
        .container {
          width: 90%;
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header-bar {
          height: 4rem;
          background-color: #068af5;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        .logo {
          display: block;
          margin: 30px auto;
          max-width: 180px;
        }
        .order-details {
          background-color: #e8f4fd;
          padding: 15px;
          border-left: 4px solid #068af5;
          margin: 20px 0;
          border-radius: 5px;
        }
        .order-details h3 {
          margin: 0 0 10px;
          color: #068af5;
        }
        .review-section {
          background-color: #f0f8ff;
          padding: 25px;
          border-radius: 8px;
          border: 2px solid #068af5;
          margin: 30px 0;
        }
        .review-form {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .rating-label {
          font-weight: bold;
          margin-bottom: 15px;
          display: block;
          color: #333;
          font-size: 16px;
        }
        
        /* Pure CSS Star Rating System */
        .star-rating {
          display: flex;
          flex-direction: row-reverse;
          justify-content: center;
          margin-bottom: 20px;
        }
        
        .star-rating input[type="radio"] {
          display: none;
        }
        
        .star-rating label {
          font-size: 35px;
          color: #ddd;
          cursor: pointer;
          margin: 0 2px;
          transition: color 0.2s;
          text-decoration: none;
        }
        
        /* When a radio button is checked, color it and all previous stars */
        .star-rating input[type="radio"]:checked ~ label,
        .star-rating label:hover,
        .star-rating label:hover ~ label {
          color: #ffc107;
        }
        
        /* Specific star selections - ensures proper filling */
        .star-rating input[value="5"]:checked ~ label {
          color: #ffc107;
        }
        .star-rating input[value="4"]:checked ~ label:nth-of-type(n+2) {
          color: #ffc107;
        }
        .star-rating input[value="3"]:checked ~ label:nth-of-type(n+3) {
          color: #ffc107;
        }
        .star-rating input[value="2"]:checked ~ label:nth-of-type(n+4) {
          color: #ffc107;
        }
        .star-rating input[value="1"]:checked ~ label:nth-of-type(5) {
          color: #ffc107;
        }

        .form-group {
          margin-bottom: 15px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          min-height: 80px;
          resize: vertical;
          box-sizing: border-box;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 12px 30px;
          border: none;
          border-radius: 5px;
          font-weight: bold;
          cursor: pointer;
          font-size: 16px;
          transition: background-color 0.3s;
          text-decoration: none;
          display: inline-block;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #888;
          margin-top: 30px;
        }
        .social-icons img {
          width: 20px;
          margin: 0 5px;
        }
        
        /* Rating instruction text */
        .rating-instruction {
          text-align: center;
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        @media screen and (max-width: 600px) {
          .container {
            padding: 20px;
          }
          .logo {
            max-width: 140px;
          }
          .star-rating label {
            font-size: 30px;
            margin: 0 1px;
          }
        }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    </head>
    <body>
      <div class="header-bar"></div>
      <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>${customerName}</strong>,</p>

        <div class="order-details">
          <h3>🎉 Great News! Your Order is Ready</h3>
          <p><strong>Order Number:</strong> ${orderNo}</p>
          <p><strong>Status:</strong> Your order is <strong>${readyMessage}</strong>.</p>
          <p>${actionMessage}</p>
        </div>

        <p>Thank you for choosing <strong>${businessName}</strong>.</p>
        ${isDelivery 
          ? `<p>Our delivery team will be reaching out to you shortly.</p>` 
          : `<p>Please visit our location to collect your order at your convenience.</p>`}

        <div class="review-section">
          <h3>📝 We Value Your Feedback!</h3>
          <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

          <div class="review-form">
            <form id="reviewForm" action="${apiBaseUrl}/api/v1/device/email-review" method="POST">
              <input type="hidden" name="deviceId" value="${deviceInfo?._id || ''}">
              <input type="hidden" name="customerId" value="${customerId}">
              <input type="hidden" name="orderId" value="${orderId}">

              <label class="rating-label">⭐ Rate Your Overall Experience</label>
              <div class="rating-instruction">Click on a star to rate (1-5 stars)</div>
              
              <div class="star-rating">
                <input type="radio" name="rating" value="5" id="star5" required>
                <label for="star5">★</label>
                <input type="radio" name="rating" value="4" id="star4">
                <label for="star4">★</label>
                <input type="radio" name="rating" value="3" id="star3">
                <label for="star3">★</label>
                <input type="radio" name="rating" value="2" id="star2">
                <label for="star2">★</label>
                <input type="radio" name="rating" value="1" id="star1">
                <label for="star1">★</label>
              </div>

              <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience at ${businessName}..."></textarea>
              </div>

              <input type="submit" class="submit-button" value="Submit Review" />
            </form>
            <p style="font-size: 13px; color: #555; margin-top: 15px;"><em>Thank you for your feedback!</em></p>
          </div>
        </div>

        <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
          <p>Follow us:</p>
          <div class="social-icons">
            <a href="https://www.linkedin.com/company/patronworks/"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn" /></a>
            <a href="https://www.facebook.com/patronworks"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook" /></a>
          </div>
          <p>&copy; ${new Date().getFullYear()} ${businessName}. All rights reserved.</p>
        </div>
      </div>
    </body>
  </html>
  `;
};