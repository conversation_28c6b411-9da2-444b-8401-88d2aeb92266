<!DOCTYPE html>
<html>
<head>
    <title>Test Review Form</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f8f8f8;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .rating-options {
            margin-bottom: 20px;
        }
        .rating-option {
            display: block;
            margin: 10px 0;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fff;
            cursor: pointer;
        }
        .rating-option:hover {
            border-color: #068af5;
            background-color: #f8f9ff;
        }
        .rating-option input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .star-display {
            font-size: 18px;
            color: #ffc107;
            margin-right: 8px;
        }
        .rating-text {
            font-size: 16px;
            color: #333;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
        }
        .submit-button {
            background-color: #068af5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            font-size: 16px;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Test Review Form</h2>
        <p><strong>Testing URL:</strong> http://localhost:4444/api/v1/device/email-review</p>
        
        <form action="http://localhost:4444/api/v1/device/email-review" method="POST" enctype="application/x-www-form-urlencoded">
            <!-- Hidden fields with test data -->
            <input type="hidden" name="deviceId" value="507f1f77bcf86cd799439011">
            <input type="hidden" name="customerId" value="507f1f77bcf86cd799439012">
            <input type="hidden" name="orderId" value="test_order_123">

            <!-- Visible test fields for debugging -->
            <div style="background: #ffe6e6; padding: 10px; margin: 10px 0; border-radius: 4px;">
                <strong>Visible Test Fields (for debugging):</strong><br>
                <label>Device ID: <input type="text" name="deviceId_visible" value="507f1f77bcf86cd799439011" readonly style="width: 200px;"></label><br><br>
                <label>Customer ID: <input type="text" name="customerId_visible" value="507f1f77bcf86cd799439012" readonly style="width: 200px;"></label><br><br>
                <label>Order ID: <input type="text" name="orderId_visible" value="test_order_123" readonly style="width: 200px;"></label>
            </div>
            
            <!-- Debug info -->
            <div class="debug-info">
                <strong>Debug Info:</strong><br>
                Device ID: 507f1f77bcf86cd799439011<br>
                Customer ID: 507f1f77bcf86cd799439012<br>
                Order ID: test_order_123<br>
                API URL: http://localhost:4444
            </div>

            <label class="form-label">⭐ Rate Your Overall Experience (Select One)</label>
            <div class="rating-options">
                <label class="rating-option" for="rating_5">
                    <input type="radio" name="rating" value="5" id="rating_5" required>
                    <span class="star-display">⭐⭐⭐⭐⭐</span>
                    <span class="rating-text">5 - Excellent</span>
                </label>

                <label class="rating-option" for="rating_4">
                    <input type="radio" name="rating" value="4" id="rating_4">
                    <span class="star-display">⭐⭐⭐⭐</span>
                    <span class="rating-text">4 - Very Good</span>
                </label>

                <label class="rating-option" for="rating_3">
                    <input type="radio" name="rating" value="3" id="rating_3">
                    <span class="star-display">⭐⭐⭐</span>
                    <span class="rating-text">3 - Good</span>
                </label>

                <label class="rating-option" for="rating_2">
                    <input type="radio" name="rating" value="2" id="rating_2">
                    <span class="star-display">⭐⭐</span>
                    <span class="rating-text">2 - Fair</span>
                </label>

                <label class="rating-option" for="rating_1">
                    <input type="radio" name="rating" value="1" id="rating_1">
                    <span class="star-display">⭐</span>
                    <span class="rating-text">1 - Poor</span>
                </label>
            </div>

            <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience..."></textarea>
            </div>

            <input type="submit" class="submit-button" value="Submit Review" />
        </form>

        <div style="margin-top: 30px; padding: 15px; background: #e8f4fd; border-radius: 5px;">
            <h3>How to Test:</h3>
            <ol>
                <li>Make sure your server is running on port 4444</li>
                <li>Select ONE radio button (they should be single-select)</li>
                <li>Optionally add a comment</li>
                <li>Click Submit Review</li>
                <li>Check server logs for the request data</li>
            </ol>
        </div>
    </div>
</body>
</html>
